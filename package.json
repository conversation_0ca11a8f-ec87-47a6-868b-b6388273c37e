{"name": "mtl-strapi-cms", "private": true, "version": "0.1.0", "description": "A Strapi application", "scripts": {"develop": "strapi develop", "start": "strapi start", "build": "strapi build", "strapi": "strapi"}, "dependencies": {"@_sh/strapi-plugin-ckeditor": "^1.1.3", "@strapi-community/strapi-provider-upload-google-cloud-storage": "^4.10.5", "@strapi/plugin-cloud": "4.24.1", "@strapi/plugin-i18n": "4.24.1", "@strapi/plugin-users-permissions": "4.24.1", "@strapi/provider-upload-aws-s3": "^5.19.0", "@strapi/strapi": "4.24.1", "better-sqlite3": "^8.6.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "5.3.4", "strapi-plugin-import-export-entries": "^1.23.1", "styled-components": "5.3.3"}, "author": {"name": "A Strapi developer"}, "strapi": {"uuid": "4d6750d4-52e9-4418-8722-2c6e3ef9b418"}, "engines": {"node": ">=18.0.0 <=20.x.x", "npm": ">=6.0.0"}, "license": "MIT", "devDependencies": {"@types/minimatch": "^6.0.0"}}