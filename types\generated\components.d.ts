import type { Schema, Attribute } from '@strapi/strapi';

export interface AiReadinessOptions extends Schema.Component {
  collectionName: 'components_ai_readiness_options';
  info: {
    displayName: 'options';
  };
  attributes: {
    name: Attribute.String;
    value: Attribute.Integer;
  };
}

export interface AiReadinessQuestion extends Schema.Component {
  collectionName: 'components_ai_readiness_questions';
  info: {
    displayName: 'question';
    description: '';
  };
  attributes: {
    name: Attribute.String;
    answers: Attribute.Component<'ai-readiness.options', true>;
    type: Attribute.Enumeration<['draggable', 'mcq']>;
    number: Attribute.Integer;
    sub_question: Attribute.Component<'ai-readiness.options', true>;
  };
}

export interface AllServicePageAllServicePage extends Schema.Component {
  collectionName: 'components_all_service_page_all_service_pages';
  info: {
    displayName: 'AllServicePage';
  };
  attributes: {
    tag: Attribute.String;
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
    button: Attribute.Component<'common.button'>;
    l_2_service_pages: Attribute.Relation<
      'all-service-page.all-service-page',
      'oneToMany',
      'api::l2-service-page.l2-service-page'
    >;
  };
}

export interface AuditMethodologyAuditMethodology extends Schema.Component {
  collectionName: 'components_audit_methodology_audit_methodologies';
  info: {
    displayName: 'Audit-methodology';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    box: Attribute.Component<'common.title-description', true>;
    ctaButtonText: Attribute.String;
  };
}

export interface BenefitsBenefits extends Schema.Component {
  collectionName: 'components_benefits_benefits';
  info: {
    displayName: 'Benefits';
  };
  attributes: {
    first_slider: Attribute.Component<'common.title-description-image', true>;
    second_slider: Attribute.Component<'common.title-description-image', true>;
    title: Attribute.String;
    description: Attribute.RichText;
  };
}

export interface BlogBlogRelatedService extends Schema.Component {
  collectionName: 'components_blog_blog_related_services';
  info: {
    displayName: 'blog_related_service';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    url: Attribute.String;
    description: Attribute.RichText;
  };
}

export interface BlogCaseStudySuggestions extends Schema.Component {
  collectionName: 'components_blog_case_study_suggestions';
  info: {
    displayName: 'caseStudy_suggestions';
    icon: 'ad';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
    cover_image: Attribute.Media;
  };
}

export interface BlogContent extends Schema.Component {
  collectionName: 'components_blog_contents';
  info: {
    displayName: 'content';
    icon: 'align-justify';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    twitter_link: Attribute.String;
    twitter_link_text: Attribute.String;
  };
}

export interface BlogSuggestions extends Schema.Component {
  collectionName: 'components_blog_suggestions';
  info: {
    displayName: 'suggestions';
    icon: 'th';
    description: '';
  };
  attributes: {
    blogs: Attribute.Relation<
      'blog.suggestions',
      'oneToMany',
      'api::blog.blog'
    >;
  };
}

export interface CareersCoreValues extends Schema.Component {
  collectionName: 'components_careers_core_values';
  info: {
    displayName: 'core_values';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    box: Attribute.Component<'common.title-description-image', true>;
  };
}

export interface CareersLifeAtMtl extends Schema.Component {
  collectionName: 'components_careers_life_at_mtls';
  info: {
    displayName: 'life_at_mtl';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    images: Attribute.Media;
  };
}

export interface CaseStudiesHeroSection extends Schema.Component {
  collectionName: 'components_case_studies_hero_sections';
  info: {
    displayName: 'hero_section';
    description: '';
  };
  attributes: {
    tag: Attribute.String;
    title: Attribute.String;
    download_button: Attribute.Component<'common.button'>;
    image: Attribute.Media;
    description: Attribute.RichText;
    global_services: Attribute.Relation<
      'case-studies.hero-section',
      'oneToMany',
      'api::global-service.global-service'
    >;
    global_industries: Attribute.Relation<
      'case-studies.hero-section',
      'oneToMany',
      'api::global-industry.global-industry'
    >;
  };
}

export interface CaseStudiesPreview extends Schema.Component {
  collectionName: 'components_case_studies_previews';
  info: {
    displayName: 'preview';
    description: '';
  };
  attributes: {
    tag: Attribute.String;
    title: Attribute.Text;
    preview_background_image: Attribute.Media;
    link: Attribute.String;
  };
}

export interface CaseStudiesQuote extends Schema.Component {
  collectionName: 'components_case_studies_quotes';
  info: {
    displayName: 'quote';
    description: '';
  };
  attributes: {
    quote_by: Attribute.String;
    rich_text: Attribute.RichText;
  };
}

export interface CaseStudyCardBox extends Schema.Component {
  collectionName: 'components_case_study_card_boxes';
  info: {
    displayName: 'cardBox';
    description: '';
  };
  attributes: {
    case_study_badge: Attribute.String;
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
    url: Attribute.String;
  };
}

export interface CaseStudyCaseStudyRelation extends Schema.Component {
  collectionName: 'components_case_study_case_study_relations';
  info: {
    displayName: 'case_study_relation';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    case_study_relation: Attribute.Relation<
      'case-study.case-study-relation',
      'oneToMany',
      'api::case-study.case-study'
    >;
    link_title: Attribute.String;
    link_url: Attribute.String;
  };
}

export interface CaseStudyCaseStudy extends Schema.Component {
  collectionName: 'components_case_study_case_studies';
  info: {
    displayName: 'Case-study';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link_title: Attribute.String;
    card_box: Attribute.Component<'case-study.card-box', true>;
    url: Attribute.String;
  };
}

export interface CaseStudyFilters extends Schema.Component {
  collectionName: 'components_case_study_filters';
  info: {
    displayName: 'filters';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    select_services_title: Attribute.String;
    select_industries_title: Attribute.String;
    search_button_title: Attribute.String;
    sort_by_text: Attribute.String;
  };
}

export interface CaseStudyHeroSection extends Schema.Component {
  collectionName: 'components_case_study_hero_sections';
  info: {
    displayName: 'hero_section';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    image: Attribute.Media;
  };
}

export interface CollabCollab extends Schema.Component {
  collectionName: 'components_collab_collabs';
  info: {
    displayName: 'collab';
    description: '';
  };
  attributes: {
    title: Attribute.String;
  };
}

export interface CommonBlogHero extends Schema.Component {
  collectionName: 'components_common_blog_heroes';
  info: {
    displayName: 'blog_hero';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    blog_tag: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
    global_services: Attribute.Relation<
      'common.blog-hero',
      'oneToMany',
      'api::global-service.global-service'
    >;
    global_industries: Attribute.Relation<
      'common.blog-hero',
      'oneToMany',
      'api::global-industry.global-industry'
    >;
  };
}

export interface CommonButtonWithImage extends Schema.Component {
  collectionName: 'components_common_button_with_images';
  info: {
    displayName: 'ButtonWithImage';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
    image: Attribute.Media;
  };
}

export interface CommonButton extends Schema.Component {
  collectionName: 'components_common_buttons';
  info: {
    displayName: 'Button';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
  };
}

export interface CommonClutchReviews extends Schema.Component {
  collectionName: 'components_common_clutch_reviews';
  info: {
    displayName: 'clutch_reviews';
  };
  attributes: {
    title: Attribute.String;
    review_image: Attribute.Media;
  };
}

export interface CommonFilter extends Schema.Component {
  collectionName: 'components_common_filters';
  info: {
    displayName: 'filter';
  };
  attributes: {};
}

export interface CommonLinkBox extends Schema.Component {
  collectionName: 'components_common_link_boxes';
  info: {
    displayName: 'LinkBox';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
    Sublinks: Attribute.Component<'common.sublinks', true>;
  };
}

export interface CommonMultipleImage extends Schema.Component {
  collectionName: 'components_common_multiple_images';
  info: {
    displayName: 'MultipleImage';
  };
  attributes: {
    images: Attribute.Media;
  };
}

export interface CommonSingleImage extends Schema.Component {
  collectionName: 'components_common_single_images';
  info: {
    displayName: 'SingleImage';
  };
  attributes: {
    image: Attribute.Media;
  };
}

export interface CommonSublinks extends Schema.Component {
  collectionName: 'components_common_sublinks';
  info: {
    displayName: 'Sublinks';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
  };
}

export interface CommonTitleDescImageLink extends Schema.Component {
  collectionName: 'components_common_title_desc_image_links';
  info: {
    displayName: 'TitleDescImageLink';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
    link: Attribute.String;
    mobile_image: Attribute.Media;
  };
}

export interface CommonTitleDescriptionImageButton extends Schema.Component {
  collectionName: 'components_common_title_description_image_buttons';
  info: {
    displayName: 'TitleDescriptionImageButton';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
    button_title: Attribute.String;
    button_link: Attribute.String;
    mobile_image: Attribute.Media;
  };
}

export interface CommonTitleDescriptionImageSlider extends Schema.Component {
  collectionName: 'components_common_title_description_image_sliders';
  info: {
    displayName: 'titleDescriptionImageSlider';
    description: '';
  };
  attributes: {
    clientName: Attribute.String;
    clientDescription: Attribute.RichText;
    image: Attribute.Media;
    testimonial_video_link: Attribute.String;
  };
}

export interface CommonTitleDescriptionImage extends Schema.Component {
  collectionName: 'components_common_title_description_images';
  info: {
    displayName: 'TitleDescriptionImage';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
    mobile_image: Attribute.Media;
  };
}

export interface CommonTitleDescription extends Schema.Component {
  collectionName: 'components_common_title_descriptions';
  info: {
    displayName: 'TitleDescription';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
  };
}

export interface CommonTitleImageRepeat extends Schema.Component {
  collectionName: 'components_common_title_image_repeats';
  info: {
    displayName: 'titleImageRepeat';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    emp_details: Attribute.Component<'common.title-description-image', true>;
  };
}

export interface CommonTitleImage extends Schema.Component {
  collectionName: 'components_common_title_images';
  info: {
    displayName: 'TitleImage';
  };
  attributes: {
    title: Attribute.String;
    image: Attribute.Media;
  };
}

export interface CommonTitle extends Schema.Component {
  collectionName: 'components_common_titles';
  info: {
    displayName: 'Title';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    email_id: Attribute.String;
  };
}

export interface CompanyStatisticsCompanyStatistics extends Schema.Component {
  collectionName: 'components_company_statistics_company_statistics';
  info: {
    displayName: 'Company Statistics';
    description: '';
  };
  attributes: {
    Title: Attribute.String;
    statisticsCards: Attribute.Component<
      'company-statistics.statistics-cards',
      true
    >;
  };
}

export interface CompanyStatisticsStatisticsCards extends Schema.Component {
  collectionName: 'components_company_statistics_statistics_cards';
  info: {
    displayName: 'Statistics Cards';
    description: '';
  };
  attributes: {
    statistics: Attribute.Float;
    suffix: Attribute.String;
    decimalValue: Attribute.Boolean;
    numbersOfDigitAfterDecimal: Attribute.Integer;
    description: Attribute.Text;
    box_title: Attribute.String;
  };
}

export interface ContactUsFormContactUsForm extends Schema.Component {
  collectionName: 'components_contact_us_form_contact_us_forms';
  info: {
    displayName: 'Contact-us-form';
    description: '';
  };
  attributes: {
    left_side_fields: Attribute.Component<'form.form-fields'>;
    title: Attribute.String;
    description: Attribute.RichText;
    button_title: Attribute.String;
    consent_text: Attribute.String;
  };
}

export interface ContactUsFormRightSideContent extends Schema.Component {
  collectionName: 'components_contact_us_form_right_side_contents';
  info: {
    displayName: 'right_side_content';
    description: '';
  };
  attributes: {
    logo: Attribute.Media;
    trusted_by_title: Attribute.String;
    trusted_by_logos: Attribute.Component<'common.multiple-image'>;
    collabration_title: Attribute.String;
    request_a_discovery_call: Attribute.String;
    collab_box: Attribute.Component<'common.title', true>;
    request_a_discovery_call_box: Attribute.Component<'common.title', true>;
    our_offices: Attribute.Component<'our-offices.our-offices'>;
    form_awards: Attribute.Component<'form-awards.form-awards'>;
  };
}

export interface CtaCta extends Schema.Component {
  collectionName: 'components_cta_ctas';
  info: {
    displayName: 'CTA';
    description: '';
  };
  attributes: {
    ctaButtonText: Attribute.String;
    ctaTitle: Attribute.RichText;
    ctaLink: Attribute.String;
  };
}

export interface EbooksPreview extends Schema.Component {
  collectionName: 'components_ebooks_previews';
  info: {
    displayName: 'preview';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    preview_image: Attribute.Media;
    link: Attribute.String & Attribute.Required;
  };
}

export interface EmployeeTestimonialEmployeeBox extends Schema.Component {
  collectionName: 'components_employee_testimonial_employee_boxes';
  info: {
    displayName: 'employee_box';
  };
  attributes: {
    emp_title: Attribute.String;
    box_description: Attribute.RichText;
    emp_image: Attribute.Media;
    emp_description: Attribute.String;
  };
}

export interface EmployeeTestimonialEmployeeTestimonial
  extends Schema.Component {
  collectionName: 'components_employee_testimonial_employee_testimonials';
  info: {
    displayName: 'Employee-testimonial';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    employee_box: Attribute.Component<
      'employee-testimonial.employee-box',
      true
    >;
  };
}

export interface EventsPagesCard extends Schema.Component {
  collectionName: 'components_events_pages_cards';
  info: {
    displayName: 'card';
  };
  attributes: {
    card_title: Attribute.String;
    card_description: Attribute.RichText;
  };
}

export interface EventsPagesHeroSection extends Schema.Component {
  collectionName: 'components_events_pages_hero_sections';
  info: {
    displayName: 'hero_section';
    description: '';
  };
  attributes: {
    hero_title: Attribute.String;
    venue_title: Attribute.String;
    booth_number_value: Attribute.String;
    button: Attribute.Component<'common.button'>;
    hero_image: Attribute.Media;
    date_title: Attribute.String;
    date_value: Attribute.String;
    booth_number_title: Attribute.String;
    venue_value: Attribute.String;
    event_starting_date: Attribute.Date;
  };
}

export interface EventsPagesOfferingsCard extends Schema.Component {
  collectionName: 'components_events_pages_offerings_cards';
  info: {
    displayName: 'offerings_card';
    description: '';
  };
  attributes: {
    card: Attribute.Component<'events-pages.card', true>;
    title: Attribute.String;
  };
}

export interface EventsPagesOurPeopleCard extends Schema.Component {
  collectionName: 'components_events_pages_our_people_cards';
  info: {
    displayName: 'our_people_card';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    logo: Attribute.Media;
    image: Attribute.Media;
    link: Attribute.String;
  };
}

export interface EventsPagesOurPeople extends Schema.Component {
  collectionName: 'components_events_pages_our_people';
  info: {
    displayName: 'our_people';
  };
  attributes: {
    our_people: Attribute.Component<'events-pages.our-people-card', true>;
    title: Attribute.String;
  };
}

export interface FaqFaqItems extends Schema.Component {
  collectionName: 'components_faq_faq_items';
  info: {
    displayName: 'faq_items';
    description: '';
  };
  attributes: {
    question: Attribute.String;
    answer: Attribute.RichText;
  };
}

export interface FaqFaq extends Schema.Component {
  collectionName: 'components_faq_faqs';
  info: {
    displayName: 'faq';
  };
  attributes: {
    title: Attribute.String;
    faq_items: Attribute.Component<'faq.faq-items', true>;
  };
}

export interface FooterFourthRow extends Schema.Component {
  collectionName: 'components_footer_fourth_rows';
  info: {
    displayName: 'CompanyLogoRow';
    description: '';
  };
  attributes: {
    image: Attribute.Media;
    link: Attribute.String;
    social_platforms: Attribute.Component<'footer.social-platforms', true>;
    Copyright: Attribute.Text;
  };
}

export interface FooterSectorBox extends Schema.Component {
  collectionName: 'components_footer_sector_boxes';
  info: {
    displayName: 'SubSectorBox';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
  };
}

export interface FooterSocialPlatforms extends Schema.Component {
  collectionName: 'components_footer_social_platforms';
  info: {
    displayName: 'SocialPlatforms';
  };
  attributes: {
    image: Attribute.Media;
    link: Attribute.String;
  };
}

export interface FooterThirdRow extends Schema.Component {
  collectionName: 'components_footer_third_rows';
  info: {
    displayName: 'TermsAndConditionRow';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
  };
}

export interface FormAwardsFormAwards extends Schema.Component {
  collectionName: 'components_form_awards_form_awards';
  info: {
    displayName: 'form_awards';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    images: Attribute.Media;
  };
}

export interface FormCaseStudyForm extends Schema.Component {
  collectionName: 'components_form_case_study_forms';
  info: {
    displayName: 'caseStudy_form';
  };
  attributes: {
    download_title: Attribute.String;
    description: Attribute.Text;
    form_values: Attribute.Component<'form.form-values'>;
    form_download_button: Attribute.Component<'common.button'>;
  };
}

export interface FormFormFields extends Schema.Component {
  collectionName: 'components_form_form_fields';
  info: {
    displayName: 'formFields';
    description: '';
  };
  attributes: {
    fieldNameFor_FirstName: Attribute.String;
    fieldNameFor_LastName: Attribute.String;
    fieldNameFor_EmailAddress: Attribute.String;
    fieldNameFor_CompanyName: Attribute.String;
    fieldNameFor_PhoneNumber: Attribute.String;
    fieldNameFor_HowCanWeHelpYou: Attribute.String;
    fieldNameFor_HowDidYouHearAboutUs: Attribute.String;
  };
}

export interface FormFormValues extends Schema.Component {
  collectionName: 'components_form_form_values';
  info: {
    displayName: 'form_values';
    description: '';
  };
  attributes: {
    fieldNameFor_FirstName: Attribute.String;
    fieldNameFor_EmailAddress: Attribute.String;
    fieldNameFor_CompanyName: Attribute.String;
    fieldNameFor_PhoneNumber: Attribute.String;
  };
}

export interface FormForm extends Schema.Component {
  collectionName: 'components_form_forms';
  info: {
    displayName: 'form';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    instructions: Attribute.Text;
    formFields: Attribute.Component<'form.form-fields'>;
    consent_statement: Attribute.Text;
    button: Attribute.Component<'common.button'>;
    LinkedInButton_title: Attribute.String;
  };
}

export interface HeaderLogo extends Schema.Component {
  collectionName: 'components_header_logos';
  info: {
    displayName: 'Logo';
    description: '';
  };
  attributes: {
    image: Attribute.Media;
    link: Attribute.String;
  };
}

export interface HeaderMenu1 extends Schema.Component {
  collectionName: 'components_header_menu_1s';
  info: {
    displayName: 'Menu_1';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
    button: Attribute.Component<'common.button-with-image'>;
    subMenu: Attribute.Component<'header.submenu', true>;
  };
}

export interface HeaderMenu2 extends Schema.Component {
  collectionName: 'components_header_menu_2s';
  info: {
    displayName: 'Menu_2';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
    subLinks: Attribute.Component<'common.sublinks', true>;
  };
}

export interface HeaderMenu3 extends Schema.Component {
  collectionName: 'components_header_menu_3s';
  info: {
    displayName: 'Menu_3';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
    subLinks: Attribute.Component<'common.sublinks', true>;
    button: Attribute.Component<'common.button-with-image'>;
    titleDescription: Attribute.Component<'common.title-description'>;
  };
}

export interface HeaderMenu4 extends Schema.Component {
  collectionName: 'components_header_menu_4s';
  info: {
    displayName: 'Menu_4';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
    subLinks: Attribute.Component<'common.sublinks', true>;
    button: Attribute.Component<'common.button-with-image'>;
  };
}

export interface HeaderMenu5 extends Schema.Component {
  collectionName: 'components_header_menu_5s';
  info: {
    displayName: 'Menu_5';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
  };
}

export interface HeaderSubmenu extends Schema.Component {
  collectionName: 'components_header_submenus';
  info: {
    displayName: 'Submenu';
  };
  attributes: {
    title: Attribute.String;
    link: Attribute.String;
    sublinks: Attribute.JSON;
  };
}

export interface HeroSectionAboutUsHeroSection extends Schema.Component {
  collectionName: 'components_hero_section_about_us_hero_sections';
  info: {
    displayName: 'AboutUsHeroSection';
  };
  attributes: {
    title: Attribute.RichText;
    description: Attribute.RichText;
    image: Attribute.Media;
    hero_tag: Attribute.String;
  };
}

export interface HeroSectionHomeHeroSection extends Schema.Component {
  collectionName: 'components_home_hero_section_home_hero_sections';
  info: {
    displayName: 'HomeHeroSection';
    description: '';
  };
  attributes: {
    title_description: Attribute.Component<'common.title-description'>;
    banner_name: Attribute.String;
    service_name: Attribute.String;
    image: Attribute.Media & Attribute.Required;
    link_url: Attribute.String & Attribute.Required;
    open_link_in_new_tab: Attribute.Boolean &
      Attribute.Required &
      Attribute.DefaultTo<false>;
  };
}

export interface IndustriesCardIndustriesCard extends Schema.Component {
  collectionName: 'components_industries_card_industries_cards';
  info: {
    displayName: 'Industries Card';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    backgroundImage: Attribute.Media;
    industriesCardsBox: Attribute.Component<
      'industries-card.industries-cards-box',
      true
    >;
  };
}

export interface ******************************** extends Schema.Component {
  collectionName: 'components_industries_card_industries_cards_boxes';
  info: {
    displayName: 'industriesCardsBox';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    button: Attribute.Component<'common.button'>;
    description: Attribute.RichText;
    backgroundImage: Attribute.Media;
  };
}

export interface InsightsInsightsSlider extends Schema.Component {
  collectionName: 'components_insights_insights_sliders';
  info: {
    displayName: 'InsightsSlider';
  };
  attributes: {
    sliderTitle: Attribute.String;
    sliderDescription: Attribute.RichText;
    sliderImage: Attribute.Media;
    viewMoreUrl: Attribute.String;
  };
}

export interface InsightsInsights extends Schema.Component {
  collectionName: 'components_insights_insights';
  info: {
    displayName: 'Insights';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.RichText;
    taglineUrl: Attribute.String;
    circular_text_image: Attribute.Media;
    blogs: Attribute.Relation<
      'insights.insights',
      'oneToMany',
      'api::blog.blog'
    >;
  };
}

export interface L2ServicesL2Services extends Schema.Component {
  collectionName: 'components_l2_services_l2_services';
  info: {
    displayName: 'L2-Services';
  };
  attributes: {
    title: Attribute.String;
    L2ServicesCard: Attribute.Component<
      'other-services.other-services-card',
      true
    >;
  };
}

export interface MeetOurTeamMeetOurPeople extends Schema.Component {
  collectionName: 'components_meet_our_people_meet_our_people';
  info: {
    displayName: 'card_box';
    description: '';
  };
  attributes: {
    card_description: Attribute.RichText;
    background_image: Attribute.Media;
    logo_image: Attribute.Media;
    card_title: Attribute.String;
    button_title: Attribute.String;
    button_link: Attribute.String;
  };
}

export interface MeetOurTeamMeetOurTeam extends Schema.Component {
  collectionName: 'components_meet_our_team_meet_our_teams';
  info: {
    displayName: 'Meet-our-team';
    description: '';
  };
  attributes: {
    main_title: Attribute.String;
    card_box: Attribute.Component<'meet-our-team.meet-our-people', true>;
  };
}

export interface OtherServicesL3OtherServicesCard extends Schema.Component {
  collectionName: 'components_other_services_l3_other_services_cards';
  info: {
    displayName: 'l3-other-services-card';
    description: '';
  };
  attributes: {
    other_services_cards: Attribute.Component<
      'other-services.other-services-card',
      true
    >;
  };
}

export interface OtherServicesL3OtherServices extends Schema.Component {
  collectionName: 'components_other_services_l3_other_services';
  info: {
    displayName: 'L3_other_services';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    other_services_card: Attribute.Component<
      'other-services.other-services-card',
      true
    >;
  };
}

export interface OtherServicesOtherServicesCard extends Schema.Component {
  collectionName: 'components_other_services_other_services_cards';
  info: {
    displayName: 'other_services_card';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    service_page_link: Attribute.String;
    on_hover_bg_image: Attribute.Media;
    description: Attribute.RichText;
  };
}

export interface OtherServicesOtherServices extends Schema.Component {
  collectionName: 'components_other_services_other_services';
  info: {
    displayName: 'other_services';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    all_services_card_title: Attribute.String;
    all_services_card_link: Attribute.String;
    other_services_card: Attribute.Component<
      'other-services.other-services-card',
      true
    >;
  };
}

export interface OurOfficesOurOffices extends Schema.Component {
  collectionName: 'components_our_offices_our_offices';
  info: {
    displayName: 'our_offices';
    description: '';
  };
  attributes: {
    india: Attribute.RichText;
    usa: Attribute.RichText;
    title: Attribute.String;
  };
}

export interface OurServicesOurServicesCard extends Schema.Component {
  collectionName: 'components_our_services_our_services_cards';
  info: {
    displayName: 'Our-services-card';
    description: '';
  };
  attributes: {
    cardTitle: Attribute.String;
    cardImage: Attribute.Media;
    cardTitle2: Attribute.String;
    cardContent: Attribute.RichText;
    url: Attribute.String;
  };
}

export interface OurServicesOurServices extends Schema.Component {
  collectionName: 'components_our_services_our_services';
  info: {
    displayName: 'Our-services';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    ourServicesCard: Attribute.Component<
      'our-services.our-services-card',
      true
    >;
    subtitle: Attribute.RichText;
  };
}

export interface PartnersNewsEvents extends Schema.Component {
  collectionName: 'components_partners_news_events';
  info: {
    displayName: 'News_events';
  };
  attributes: {
    news: Attribute.Relation<
      'partners.news-events',
      'oneToMany',
      'api::new.new'
    >;
    event_main_pages: Attribute.Relation<
      'partners.news-events',
      'oneToMany',
      'api::event-main-page.event-main-page'
    >;
  };
}

export interface PodcastPageHeroSection extends Schema.Component {
  collectionName: 'components_podcast_page_hero_sections';
  info: {
    displayName: 'hero_section';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
  };
}

export interface PodcastPageLatestEpisode extends Schema.Component {
  collectionName: 'components_podcast_page_latest_episodes';
  info: {
    displayName: 'latest_episode';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    youtube_video_embed_link: Attribute.String;
    video_thumbnail_image: Attribute.Media;
    subtitle: Attribute.RichText;
  };
}

export interface PodcastPageLinks extends Schema.Component {
  collectionName: 'components_podcast_page_links';
  info: {
    displayName: 'links';
    description: '';
  };
  attributes: {
    icon: Attribute.Media;
    url: Attribute.String;
  };
}

export interface PodcastPageListenOn extends Schema.Component {
  collectionName: 'components_podcast_page_listen_ons';
  info: {
    displayName: 'listen_on';
  };
  attributes: {
    title: Attribute.String;
    links: Attribute.Component<'podcast-page.links', true>;
  };
}

export interface PodcastPagePodcastEpisode extends Schema.Component {
  collectionName: 'components_podcast_page_podcast_episodes';
  info: {
    displayName: 'podcast_episode';
    description: '';
  };
  attributes: {
    episode_listing: Attribute.String;
    youtube_video_embed_link: Attribute.String;
    title: Attribute.String;
    description: Attribute.RichText;
    spotify_audio_full_url: Attribute.String;
    video_thumbnail_image: Attribute.Media;
    podcast_audio_file: Attribute.Media;
  };
}

export interface PodcastPagePodcastSeries extends Schema.Component {
  collectionName: 'components_podcast_page_podcast_series';
  info: {
    displayName: 'podcast_series';
    description: '';
  };
  attributes: {
    podcast_series_title: Attribute.String;
    podcast_episode: Attribute.Component<'podcast-page.podcast-episode', true>;
    layout: Attribute.Enumeration<
      ['Video: Left & Content: Right', 'Content: Left & Video: Right']
    > &
      Attribute.Required &
      Attribute.DefaultTo<'Video: Left & Content: Right'>;
    variant: Attribute.Enumeration<['black', 'white']> &
      Attribute.Required &
      Attribute.DefaultTo<'black'>;
  };
}

export interface PrAndNewsCards extends Schema.Component {
  collectionName: 'components_pr_and_news_cards';
  info: {
    displayName: 'cards';
  };
  attributes: {
    prs: Attribute.Relation<'pr-and-news.cards', 'oneToMany', 'api::pr.pr'>;
    news: Attribute.Relation<'pr-and-news.cards', 'oneToMany', 'api::new.new'>;
  };
}

export interface PrAndNewsPrAndNews extends Schema.Component {
  collectionName: 'components_pr_and_news_pr_and_news';
  info: {
    displayName: 'pr_and_news';
  };
  attributes: {
    title: Attribute.String;
    cards: Attribute.Component<'pr-and-news.cards'>;
  };
}

export interface RecognitionAwardsRecognitionsAwards extends Schema.Component {
  collectionName: 'components_recognition_awards_recognitions_awards';
  info: {
    displayName: 'Recognitions-Awards';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    awards_box: Attribute.Component<'common.title-image', true>;
  };
}

export interface ResourcesFilterResourcesFilter extends Schema.Component {
  collectionName: 'components_resources_filter_resources_filters';
  info: {
    displayName: 'resources_filter';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    select_services_title: Attribute.String;
    select_industries_title: Attribute.String;
    search_button_title: Attribute.String;
    sort_by_text: Attribute.String;
    select_resources_title: Attribute.String;
  };
}

export interface ResourcesPageSliderResourcesPageSlider
  extends Schema.Component {
  collectionName: 'components_resources_page_slider_resources_page_sliders';
  info: {
    displayName: 'resources_page_slider';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    banner_name: Attribute.String;
    service_name: Attribute.String;
    link: Attribute.String;
  };
}

export interface RichTextRichText extends Schema.Component {
  collectionName: 'components_rich_text_rich_texts';
  info: {
    displayName: 'rich_text';
    description: '';
  };
  attributes: {
    rich_text: Attribute.RichText;
    button: Attribute.Component<'common.button'>;
    vimeoVideoLink: Attribute.Text;
  };
}

export interface RichTextRichtextWithTitle extends Schema.Component {
  collectionName: 'components_rich_text_richtext_with_titles';
  info: {
    displayName: 'richtext_with_title';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    rich_text: Attribute.RichText;
    background: Attribute.Enumeration<['black', 'gray']>;
  };
}

export interface SearchCustomDataForInitialSearchResults
  extends Schema.Component {
  collectionName: 'components_search_custom_data_for_initial_search_results';
  info: {
    displayName: 'custom_data_for_initial_search_results';
    description: '';
  };
  attributes: {
    page_type_title: Attribute.String;
    page_title: Attribute.String;
    page_full_url: Attribute.String & Attribute.Required;
  };
}

export interface SearchInitialSearchResults extends Schema.Component {
  collectionName: 'components_search_initial_search_results';
  info: {
    displayName: 'initial_search_results';
    description: '';
  };
  attributes: {
    blogs: Attribute.Relation<
      'search.initial-search-results',
      'oneToMany',
      'api::blog.blog'
    >;
    case_studies: Attribute.Relation<
      'search.initial-search-results',
      'oneToMany',
      'api::case-study.case-study'
    >;
    l2_service_pages: Attribute.Relation<
      'search.initial-search-results',
      'oneToMany',
      'api::l2-service-page.l2-service-page'
    >;
    l3_service_pages: Attribute.Relation<
      'search.initial-search-results',
      'oneToMany',
      'api::l3-service-page.l3-service-page'
    >;
    industries: Attribute.Relation<
      'search.initial-search-results',
      'oneToMany',
      'api::industry.industry'
    >;
    partners: Attribute.Relation<
      'search.initial-search-results',
      'oneToMany',
      'api::partner.partner'
    >;
    custom_data_for_initial_search_results: Attribute.Component<
      'search.custom-data-for-initial-search-results',
      true
    > &
      Attribute.SetMinMax<
        {
          max: 5;
        },
        number
      >;
    Instructions_doNotChange: Attribute.Text & Attribute.Private;
  };
}

export interface SeoKeywords extends Schema.Component {
  collectionName: 'components_seo_keywords';
  info: {
    displayName: 'keywords';
    icon: 'arrow-alt-circle-right';
    description: '';
  };
  attributes: {
    keyword: Attribute.Text;
  };
}

export interface SeoMetaProperties extends Schema.Component {
  collectionName: 'components_seo_meta_properties';
  info: {
    displayName: 'metaProperties';
    icon: 'angle-double-right';
  };
  attributes: {
    name: Attribute.String;
    content: Attribute.Text;
  };
}

export interface SeoSeo extends Schema.Component {
  collectionName: 'components_seo_seos';
  info: {
    displayName: 'seo';
    icon: 'scroll';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Text;
    type: Attribute.String;
    url: Attribute.String;
    site_name: Attribute.String;
    locale: Attribute.String;
    metaProperties: Attribute.Component<'seo.meta-properties', true>;
    keywords: Attribute.Component<'seo.keywords'>;
    image: Attribute.Media;
    schema: Attribute.JSON;
  };
}

export interface ServiceDeliveryProcessCards extends Schema.Component {
  collectionName: 'components_service_delivery_process_cards';
  info: {
    displayName: 'cards';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    number: Attribute.String;
  };
}

export interface ServiceDeliveryProcessServiceDeliveryProcess
  extends Schema.Component {
  collectionName: 'components_service_delivery_process_service_delivery_processes';
  info: {
    displayName: 'service_delivery_process';
  };
  attributes: {
    title: Attribute.String;
    cards: Attribute.Component<'service-delivery-process.cards', true>;
  };
}

export interface SolutionPageChallengesBoxSlider extends Schema.Component {
  collectionName: 'components_solution_page_challenges_box_sliders';
  info: {
    displayName: 'challenges_box_slider';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
  };
}

export interface SolutionPageChallenges extends Schema.Component {
  collectionName: 'components_solution_page_challenges';
  info: {
    displayName: 'Challenges';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    challenges_box: Attribute.Component<
      'solution-page.challenges-box-slider',
      true
    >;
  };
}

export interface TabChallengesTabChallenges extends Schema.Component {
  collectionName: 'components_tab_challenges_tab_challenges';
  info: {
    displayName: 'tab-challenges';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    tab_box: Attribute.Component<'tab-challenges.tab', true>;
  };
}

export interface TabChallengesTab extends Schema.Component {
  collectionName: 'components_tab_challenges_tabs';
  info: {
    displayName: 'tab';
  };
  attributes: {
    card_title: Attribute.String;
    card_image: Attribute.Media;
    challenges_title: Attribute.String;
    challenges_description: Attribute.RichText;
    solution_title: Attribute.String;
    solution_description: Attribute.RichText;
  };
}

export interface TechStackTab extends Schema.Component {
  collectionName: 'components_tech_stack_tabs';
  info: {
    displayName: 'tab';
    description: '';
  };
  attributes: {
    tab_title: Attribute.String;
    logo_url: Attribute.JSON;
  };
}

export interface TechStackTechStack extends Schema.Component {
  collectionName: 'components_tech_stack_tech_stacks';
  info: {
    displayName: 'tech_stack';
  };
  attributes: {
    title: Attribute.String;
    tab: Attribute.Component<'tech-stack.tab', true>;
  };
}

export interface TestimonialsTestimonials extends Schema.Component {
  collectionName: 'components_testimonials_testimonials';
  info: {
    displayName: 'Testimonials';
    description: '';
  };
  attributes: {
    testimonials_slider: Attribute.Component<
      'common.title-description-image-slider',
      true
    >;
    title: Attribute.RichText;
    testimonial_playbtn_logo: Attribute.Media;
    circular_text_line_svg: Attribute.Media;
    tagline_url: Attribute.String;
  };
}

export interface ThankYouThankYou extends Schema.Component {
  collectionName: 'components_thank_you_thank_yous';
  info: {
    displayName: 'Thank-you';
    description: '';
  };
  attributes: {
    description: Attribute.RichText;
    button: Attribute.Component<'common.button'>;
    title: Attribute.Text;
  };
}

export interface TrustedPartnerTrustedPartner extends Schema.Component {
  collectionName: 'components_trusted_partner_trusted_partners';
  info: {
    displayName: 'TrustedPartner';
    description: '';
  };
  attributes: {
    partnersLogo: Attribute.Component<'common.multiple-image'>;
    title: Attribute.String;
  };
}

export interface VideoPageAllVideos extends Schema.Component {
  collectionName: 'components_video_page_all_videos';
  info: {
    displayName: 'all_videos';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    select_video_tags_title: Attribute.String;
    sort_by_text: Attribute.String;
  };
}

export interface VisionMissionVisionMission extends Schema.Component {
  collectionName: 'components_vision_mission_vision_missions';
  info: {
    displayName: 'Vision-mission';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media;
  };
}

export interface WhyChooseMtlCards extends Schema.Component {
  collectionName: 'components_why_choose_mtl_cards';
  info: {
    displayName: 'Cards';
  };
  attributes: {
    cardTitle: Attribute.RichText;
    cardDescription: Attribute.RichText;
  };
}

export interface WhyChooseMtlWhyChooseMtl extends Schema.Component {
  collectionName: 'components_why_choose_mtl_why_choose_mtls';
  info: {
    displayName: 'Why-choose-MTL';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    subtitle: Attribute.RichText;
    whyChooseMtlCards: Attribute.Component<'why-choose-mtl.cards', true> &
      Attribute.SetMinMax<
        {
          max: 8;
        },
        number
      >;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface Components {
      'ai-readiness.options': AiReadinessOptions;
      'ai-readiness.question': AiReadinessQuestion;
      'all-service-page.all-service-page': AllServicePageAllServicePage;
      'audit-methodology.audit-methodology': AuditMethodologyAuditMethodology;
      'benefits.benefits': BenefitsBenefits;
      'blog.blog-related-service': BlogBlogRelatedService;
      'blog.case-study-suggestions': BlogCaseStudySuggestions;
      'blog.content': BlogContent;
      'blog.suggestions': BlogSuggestions;
      'careers.core-values': CareersCoreValues;
      'careers.life-at-mtl': CareersLifeAtMtl;
      'case-studies.hero-section': CaseStudiesHeroSection;
      'case-studies.preview': CaseStudiesPreview;
      'case-studies.quote': CaseStudiesQuote;
      'case-study.card-box': CaseStudyCardBox;
      'case-study.case-study-relation': CaseStudyCaseStudyRelation;
      'case-study.case-study': CaseStudyCaseStudy;
      'case-study.filters': CaseStudyFilters;
      'case-study.hero-section': CaseStudyHeroSection;
      'collab.collab': CollabCollab;
      'common.blog-hero': CommonBlogHero;
      'common.button-with-image': CommonButtonWithImage;
      'common.button': CommonButton;
      'common.clutch-reviews': CommonClutchReviews;
      'common.filter': CommonFilter;
      'common.link-box': CommonLinkBox;
      'common.multiple-image': CommonMultipleImage;
      'common.single-image': CommonSingleImage;
      'common.sublinks': CommonSublinks;
      'common.title-desc-image-link': CommonTitleDescImageLink;
      'common.title-description-image-button': CommonTitleDescriptionImageButton;
      'common.title-description-image-slider': CommonTitleDescriptionImageSlider;
      'common.title-description-image': CommonTitleDescriptionImage;
      'common.title-description': CommonTitleDescription;
      'common.title-image-repeat': CommonTitleImageRepeat;
      'common.title-image': CommonTitleImage;
      'common.title': CommonTitle;
      'company-statistics.company-statistics': CompanyStatisticsCompanyStatistics;
      'company-statistics.statistics-cards': CompanyStatisticsStatisticsCards;
      'contact-us-form.contact-us-form': ContactUsFormContactUsForm;
      'contact-us-form.right-side-content': ContactUsFormRightSideContent;
      'cta.cta': CtaCta;
      'ebooks.preview': EbooksPreview;
      'employee-testimonial.employee-box': EmployeeTestimonialEmployeeBox;
      'employee-testimonial.employee-testimonial': EmployeeTestimonialEmployeeTestimonial;
      'events-pages.card': EventsPagesCard;
      'events-pages.hero-section': EventsPagesHeroSection;
      'events-pages.offerings-card': EventsPagesOfferingsCard;
      'events-pages.our-people-card': EventsPagesOurPeopleCard;
      'events-pages.our-people': EventsPagesOurPeople;
      'faq.faq-items': FaqFaqItems;
      'faq.faq': FaqFaq;
      'footer.fourth-row': FooterFourthRow;
      'footer.sector-box': FooterSectorBox;
      'footer.social-platforms': FooterSocialPlatforms;
      'footer.third-row': FooterThirdRow;
      'form-awards.form-awards': FormAwardsFormAwards;
      'form.case-study-form': FormCaseStudyForm;
      'form.form-fields': FormFormFields;
      'form.form-values': FormFormValues;
      'form.form': FormForm;
      'header.logo': HeaderLogo;
      'header.menu-1': HeaderMenu1;
      'header.menu-2': HeaderMenu2;
      'header.menu-3': HeaderMenu3;
      'header.menu-4': HeaderMenu4;
      'header.menu-5': HeaderMenu5;
      'header.submenu': HeaderSubmenu;
      'hero-section.about-us-hero-section': HeroSectionAboutUsHeroSection;
      'hero-section.home-hero-section': HeroSectionHomeHeroSection;
      'industries-card.industries-card': IndustriesCardIndustriesCard;
      'industries-card.industries-cards-box': ********************************;
      'insights.insights-slider': InsightsInsightsSlider;
      'insights.insights': InsightsInsights;
      'l2-services.l2-services': L2ServicesL2Services;
      'meet-our-team.meet-our-people': MeetOurTeamMeetOurPeople;
      'meet-our-team.meet-our-team': MeetOurTeamMeetOurTeam;
      'other-services.l3-other-services-card': OtherServicesL3OtherServicesCard;
      'other-services.l3-other-services': OtherServicesL3OtherServices;
      'other-services.other-services-card': OtherServicesOtherServicesCard;
      'other-services.other-services': OtherServicesOtherServices;
      'our-offices.our-offices': OurOfficesOurOffices;
      'our-services.our-services-card': OurServicesOurServicesCard;
      'our-services.our-services': OurServicesOurServices;
      'partners.news-events': PartnersNewsEvents;
      'podcast-page.hero-section': PodcastPageHeroSection;
      'podcast-page.latest-episode': PodcastPageLatestEpisode;
      'podcast-page.links': PodcastPageLinks;
      'podcast-page.listen-on': PodcastPageListenOn;
      'podcast-page.podcast-episode': PodcastPagePodcastEpisode;
      'podcast-page.podcast-series': PodcastPagePodcastSeries;
      'pr-and-news.cards': PrAndNewsCards;
      'pr-and-news.pr-and-news': PrAndNewsPrAndNews;
      'recognition-awards.recognitions-awards': RecognitionAwardsRecognitionsAwards;
      'resources-filter.resources-filter': ResourcesFilterResourcesFilter;
      'resources-page-slider.resources-page-slider': ResourcesPageSliderResourcesPageSlider;
      'rich-text.rich-text': RichTextRichText;
      'rich-text.richtext-with-title': RichTextRichtextWithTitle;
      'search.custom-data-for-initial-search-results': SearchCustomDataForInitialSearchResults;
      'search.initial-search-results': SearchInitialSearchResults;
      'seo.keywords': SeoKeywords;
      'seo.meta-properties': SeoMetaProperties;
      'seo.seo': SeoSeo;
      'service-delivery-process.cards': ServiceDeliveryProcessCards;
      'service-delivery-process.service-delivery-process': ServiceDeliveryProcessServiceDeliveryProcess;
      'solution-page.challenges-box-slider': SolutionPageChallengesBoxSlider;
      'solution-page.challenges': SolutionPageChallenges;
      'tab-challenges.tab-challenges': TabChallengesTabChallenges;
      'tab-challenges.tab': TabChallengesTab;
      'tech-stack.tab': TechStackTab;
      'tech-stack.tech-stack': TechStackTechStack;
      'testimonials.testimonials': TestimonialsTestimonials;
      'thank-you.thank-you': ThankYouThankYou;
      'trusted-partner.trusted-partner': TrustedPartnerTrustedPartner;
      'video-page.all-videos': VideoPageAllVideos;
      'vision-mission.vision-mission': VisionMissionVisionMission;
      'why-choose-mtl.cards': WhyChooseMtlCards;
      'why-choose-mtl.why-choose-mtl': WhyChooseMtlWhyChooseMtl;
    }
  }
}
